import EditableContent from '@components/EditableContent';
import {JobDependencyCycleEnum} from '@pages/JobWorkflow/constants';
import {IAppState} from '@store/index';
import {InputNumber, Select} from 'acud';
import {useMemoizedFn} from 'ahooks';
import React, {useEffect} from 'react';
import {useSelector} from 'react-redux';

/**
 * 检查周期
 * @param param0  type: 检查周期  value: 检查周期值  onChange: 检查周期值改变  initTime: 需要初始化数据
 * @returns
 */
const DependencyTaskParamsDateValue: React.FC<{
  type: JobDependencyCycleEnum;
  initTime?: number;
  value?: string;
  onChange?: (value: string) => void;
}> = ({type, value, onChange, initTime}) => {
  const THIS_MONTH_BETWEEN = 'thisMonthBetween';
  const LAST_MONTH_BETWEEN = 'lastMonthBetween';
  const TO_STR = 'To';
  const DAY_STR = 'Day';

  // 是否编辑
  const isEditing = useSelector((state: IAppState) => state.workflowSlice.isEditing);

  const [selectType, setSelectType] = React.useState('');
  const [beginDay, setBeginDay] = React.useState(1);
  const [endDay, setEndDay] = React.useState(1);
  // 检查周期选项
  const cycleOptions = {
    [JobDependencyCycleEnum.HOURLY]: [
      {
        label: '当前小时',
        value: 'currentHour'
      },
      {
        label: '前1小时',
        value: 'last1Hour'
      },
      {
        label: '前2小时',
        value: 'last2Hours'
      },
      {
        label: '前3小时',
        value: 'last3Hours'
      },
      {
        label: '前12小时',
        value: 'last12Hours'
      },
      {
        label: '前24小时',
        value: 'last24Hours'
      }
    ],
    [JobDependencyCycleEnum.DAILY]: [
      {
        label: '今天',
        value: 'today'
      },
      {
        label: '昨天(昨天0点-24点)',
        value: 'last1Days'
      },
      {
        label: '前两天(前天0点-昨天24点)',
        value: 'last2Days'
      },
      {
        label: '前三天(前三天0点-昨天24点)',
        value: 'last3Days'
      },
      {
        label: '前七天(前七天0点-昨天24点)',
        value: 'last7Days'
      }
    ],
    [JobDependencyCycleEnum.WEEKLY]: [
      {
        label: '本周',
        value: 'thisWeek'
      },
      {
        label: '上一周（上周一到上周日）',
        value: 'lastWeek'
      },
      {
        label: '上二周（上二周一到上周日）',
        value: 'last2Week'
      },
      {
        label: '上三周（上三周一到上周日）',
        value: 'last3Week'
      }
    ],
    [JobDependencyCycleEnum.MONTHLY]: [
      {
        label: '本月',
        value: THIS_MONTH_BETWEEN
      },
      {
        label: '上月',
        value: LAST_MONTH_BETWEEN
      }
    ]
  };
  // 处理value
  const changeValueFn = useMemoizedFn((v: string) => {
    if (type === JobDependencyCycleEnum.MONTHLY) {
      let beginDay = 1;
      let endDay = 31;
      if (v?.startsWith(THIS_MONTH_BETWEEN)) {
        setSelectType(THIS_MONTH_BETWEEN);
        const arr = v.split(THIS_MONTH_BETWEEN)[1].split(TO_STR);

        beginDay = Number(arr[0]);
        endDay = Number(arr[1].split(DAY_STR)[0]);
      } else if (v?.startsWith(LAST_MONTH_BETWEEN)) {
        setSelectType(LAST_MONTH_BETWEEN);
        const arr = v.split(LAST_MONTH_BETWEEN)[1].split(TO_STR);
        beginDay = Number(arr[0]);
        endDay = Number(arr[1].split(DAY_STR)[0]);
      } else {
        setSelectType(v);
      }
      setBeginDay(beginDay);
      setEndDay(endDay);
    } else {
      setSelectType(v);
    }
  });
  useEffect(() => {
    changeValueFn(value);
  }, [value]);

  // 初始化数据
  const initValueFn = useMemoizedFn(() => {
    if (!type) {
      return;
    }
    let changeValue = '';
    switch (type) {
      case JobDependencyCycleEnum.MONTHLY:
        changeValue = THIS_MONTH_BETWEEN + 1 + TO_STR + 31 + DAY_STR;
        break;
      case JobDependencyCycleEnum.DAILY:
        changeValue = cycleOptions[JobDependencyCycleEnum.DAILY][0].value;
        break;
      case JobDependencyCycleEnum.HOURLY:
        changeValue = cycleOptions[JobDependencyCycleEnum.HOURLY][0].value;
        break;
      case JobDependencyCycleEnum.WEEKLY:
        changeValue = cycleOptions[JobDependencyCycleEnum.WEEKLY][0].value;
        break;
      default:
        changeValue = '';
        break;
    }

    onChange?.(changeValue);

    if (type === JobDependencyCycleEnum.MONTHLY) {
      setSelectType(THIS_MONTH_BETWEEN);
      setBeginDay(1);
      setEndDay(31);
    } else {
      setSelectType(changeValue);
    }

    console.log(changeValue);
  });

  // 切换检查周期时，初始化数据
  useEffect(() => {
    initValueFn();
  }, [initTime, initValueFn]);

  // 处理月度检查周期
  const changeMonthValue = useMemoizedFn((selectType: string, beginDay: number, endDay: number) => {
    setSelectType(selectType);
    setBeginDay(beginDay);
    setEndDay(endDay);
    onChange?.(selectType + beginDay + TO_STR + endDay + DAY_STR);
  });

  return (
    <>
      {type === JobDependencyCycleEnum.MONTHLY ? (
        <EditableContent
          value={value}
          isEditing={isEditing}
          dealValue={() =>
            `${cycleOptions[JobDependencyCycleEnum.MONTHLY]?.find((item) => item.value === selectType)?.label}${beginDay}-${endDay}号`
          }
        >
          <div style={{display: 'flex', alignItems: 'center'}}>
            <Select
              style={{flex: '1 1 70px', width: '78px'}}
              options={cycleOptions[JobDependencyCycleEnum.MONTHLY]}
              value={selectType}
              onSelect={(v) => changeMonthValue(v, beginDay, endDay)}
            />
            <InputNumber
              style={{flex: '1 1 70px', width: '100%'}}
              min={1}
              max={31}
              precision={0}
              value={beginDay}
              onChange={(v) => {
                if (!v) {
                  v = 1;
                }
                changeMonthValue(selectType, v, Math.max(v, endDay));
              }}
            />
            <InputNumber
              style={{flex: '1 1 70px', width: '100%'}}
              min={1}
              max={31}
              precision={0}
              value={endDay}
              onChange={(v) => {
                if (!v) {
                  v = 1;
                }
                changeMonthValue(selectType, Math.min(v, beginDay), v);
              }}
            />
          </div>
        </EditableContent>
      ) : (
        <EditableContent
          value={value}
          isEditing={isEditing}
          dealValue={() => cycleOptions[type]?.find((item) => item.value === selectType)?.label}
        >
          <div>
            <Select
              className="w-full"
              options={cycleOptions[type] || []}
              value={selectType}
              onSelect={(v) => {
                onChange?.(v);
              }}
            />
          </div>
        </EditableContent>
      )}
    </>
  );
};

export default DependencyTaskParamsDateValue;
