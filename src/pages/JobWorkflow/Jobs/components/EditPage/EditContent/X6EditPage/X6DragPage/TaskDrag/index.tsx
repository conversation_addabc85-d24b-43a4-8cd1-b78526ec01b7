import {Graph} from '@antv/x6';
import React from 'react';

import {Dnd} from '@antv/x6-plugin-dnd';

import IconSvg from '@components/IconSvg';
import {
  JobDependencyCycleEnum,
  JobDependencyFailurePolicyEnum,
  JobNodeTypeEnum,
  JobTaskType,
  NODE_SIZE,
  SparkConfObj,
  X6ShapeTypeEnum
} from '@pages/JobWorkflow/constants';
import {getNewId, getNewTaskName} from '../../tools/copy';
import {handleMouseDown} from '../clickFn';
import styles from './index.module.less';

//定义类型
interface IX6DragProps {
  graph?: React.RefObject<Graph>;
  dnd?: React.RefObject<Dnd>;
  ref?: React.RefObject<HTMLDivElement>;
}

// 初始化任务参数
const INIT_TASK_PARAM = {
  [JobNodeTypeEnum.DATAFLOW_TASK]: {
    parallel: 5,
    clusterList: [
      {
        engineType: 'RAY',
        clusterType: 'RESIDENT'
      }
    ]
  },
  [JobNodeTypeEnum.RAY_TASK]: {
    codePath: '',
    entryPoint: '',
    envVars: [{key: '', value: ''}],
    clusterList: [{engineType: 'RAY', clusterType: 'RESIDENT'}]
  },
  [JobNodeTypeEnum.NOTEBOOK_TASK]: {
    jupyterFilePath: '',
    clusterList: [{engineType: 'RAY', clusterType: 'RESIDENT'}]
  },
  [JobNodeTypeEnum.DEPENDENT_TASK]: {
    workspaceId: null,
    jobId: null,
    depTaskId: null,
    cycle: JobDependencyCycleEnum.HOURLY,
    dateValue: 'currentHour',
    checkInterval: 60,
    failurePolicy: JobDependencyFailurePolicyEnum.DEPENDENT_FAILURE_WAITING,
    failureWaitingTime: 10
  },
  [JobNodeTypeEnum.FILE_INTEGRATION_TASK]: {
    integrationJobId: null
  },
  [JobNodeTypeEnum.SPARK_JAR_TASK]: {
    clusterList: [],
    dependentLibraries: [''],
    envVars: [{key: '', value: ''}],
    sparkConf: [{key: '', value: ''}]
  },
  [JobNodeTypeEnum.AIHC_TASK]: {
    templateJobId: '',
    jobName: '',
    command: '',
    resourcePoolId: '',
    queue: '',
    envs: [
      {
        name: '',
        value: ''
      }
    ],
    dataSources: []
  }
};
// 任务 拖拽组件
const TaskDrag: React.FC<IX6DragProps> = ({graph, dnd, ref}) => {
  // 拖拽组件节点
  const startDrag = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    const target = e.currentTarget;
    const type: JobNodeTypeEnum = target.getAttribute('data-type') as JobNodeTypeEnum;
    const id = getNewId();
    const name = getNewTaskName(type);
    // 初始化数据
    const node = graph?.current?.createNode({
      id,
      shape: X6ShapeTypeEnum.TASK,
      data: {
        name,
        type,
        params: {
          id,
          name,
          type,
          description: '',
          taskParam: INIT_TASK_PARAM[type]
        }
      },
      width: NODE_SIZE[X6ShapeTypeEnum.TASK].width,
      height: NODE_SIZE[X6ShapeTypeEnum.TASK].height
    });

    handleMouseDown(e, node);
    dnd?.current?.start(node, e.nativeEvent as any);
  };
  const taskTypeList = [
    JobNodeTypeEnum.FILE_INTEGRATION_TASK,
    JobNodeTypeEnum.TABLE_INTEGRATION_TASK,
    JobNodeTypeEnum.DATAFLOW_TASK,
    JobNodeTypeEnum.NOTEBOOK_TASK,
    JobNodeTypeEnum.RAY_TASK,
    JobNodeTypeEnum.SPARK_JAR_TASK,
    JobNodeTypeEnum.DEPENDENT_TASK
  ];

  return (
    <div className={styles['dnd-content']} ref={ref}>
      {taskTypeList.map((item) => (
        <div
          key={item}
          data-type={JobTaskType[item].value}
          className={styles['node']}
          onMouseDown={startDrag}
        >
          <span className={styles['node-icon']}>
            <IconSvg size={20} fill="none" type={JobTaskType[item].icon} />
          </span>
          <span className={styles['node-text']}>{JobTaskType[item].label}</span>
        </div>
      ))}
    </div>
  );
};

export default React.memo(TaskDrag);
