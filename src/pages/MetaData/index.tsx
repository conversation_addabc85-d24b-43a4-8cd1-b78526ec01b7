/**
 * 元数据页面主入口
 * 页面整体组件架构分为左侧树和右侧面板两部分：
 *
 * 1 左侧树 (LeftTree)
 *   - 用于展示元数据的树形结构，支持展开、折叠和节点选择等。
 *
 * 2 右侧面板 (RightContent)
 *   - 右侧面板包含多个子组件，用于展示和操作元数据的详细信息：
 *      * 面包屑导航 (MetaBreadcrumb): 显示当前元数据的路径导航。
 *
 *      1 Catalog面板 (PanelCatalog): 展示元数据的 Catalog 信息，包含以下子组件：
 *        - 顶部操作栏 (MetaActionBar): 提供操作按钮，如刷新、新增、删除等。
 *        - Tab 导航栏 (MetaTabs): 用于切换不同的 Tab 面板。
 *        - TabPanel 1: 第一个 Tab 面板，展示具体内容。
 *        - TabPanel 2: 第二个 Tab 面板，展示具体内容。
 *      2 Schema面板 (PanelSchema)
 *      3 Table面板 (PanelTable)
 *      4 Volume面板 (PanelVolume)
 *      5 Operator面板 (PanelOperator)
 *
 * <AUTHOR>
 */

import useUrlState from '@ahooksjs/use-url-state';
import DividerLayout from '@components/LayoutLeftDividerContent';

import LeftTree from './LeftTree';
import RightContent from './RightContent';

import './index.less';
import {useCallback, useContext, useEffect, useRef, useState} from 'react';
import {IIamUsers, privateUserList, queryIamUserList} from '@api/workspace';
import {queryMetastore} from '@api/metastore';
import useAuth from '@hooks/useAuth';
import {useDocumentVisibility} from 'ahooks';
import {
  CatalogType,
  EnumMetaType,
  getCatalogDetail,
  getOperatorDetail,
  getSchemaDetail,
  getTableDetail,
  getVolumeDetail,
  getDatasetDetail,
  getModelDetail
} from '@api/metaRequest';
import {WorkspaceContext} from '..';
import flags from '@/flags';
import useWorkspaceAuth from '@hooks/useWorkspaceAuth';
import {Privilege} from '@api/permission/type';

// TreeNode 的枚举类型
export const enum EnumNodeType {
  TABLE = 'table',
  VOLUME = 'volume',
  OPERATOR = 'operator',
  DATASET = 'dataset',
  MODEL = 'model'
}

export interface IUrlStateHandler {
  urlState: {
    catalog?: string; // catalog （第一层）
    schema?: string; // schema（第二层）
    type?: `${EnumNodeType}`; // node 的类型
    node?: string; // node（第三层）
    version?: string; // 第三层节点有版本，需要区分
    tab?: string; // tab页面
    refresh?: boolean; // 是否需要刷新
    path?: string; // path
    isUnassigned?: boolean; // 是否是未授权
    isDoris?: boolean; // 是否是 Doris
    workspaceId?: string; // workspaceId
  };
  changeUrlFun: any; // 更改 URL 方法
  changeUrlFunReplace?: any; // 更改 URL 方法
  handleTreeRefresh?: () => void;
  userList?: IIamUsers[];
  hasMetastore?: boolean;
  canWrite?: boolean;
}

const klass = 'work-meta-data';

const MetaData = () => {
  const [navigateMode, setNavigateMode] = useState<'replace' | 'push'>('push');
  const [urlState, changeUrlFun] = useUrlState<IUrlStateHandler['urlState']>(
    {},
    {navigateMode: navigateMode}
  );
  // 获取工作空间ID
  const {workspaceId} = useContext(WorkspaceContext);

  const [userList, setUserList] = useState<IIamUsers[]>([]);

  const [hasMetastore, setHasMetastore] = useState(false);

  const workspaceAuth = useAuth('workspace', 'catalog');

  const canWrite = workspaceAuth === 'readWrite';

  const authList = useWorkspaceAuth([Privilege.CreateCatalog]);

  const treeRef = useRef<any>();

  const handleTreeRefresh = useCallback(() => {
    treeRef?.current && treeRef?.current?.refresh();
  }, []);

  const changeUrlFunReplace = useCallback(
    (s: any, refresh = false) => {
      setNavigateMode('replace');
      setTimeout(() => {
        changeUrlFun(s);
        setNavigateMode('push');
        if (refresh) {
          setTimeout(() => {
            handleTreeRefresh();
          }, 0);
        }
      }, 0);
    },
    [changeUrlFun, handleTreeRefresh]
  );

  const visibility = useDocumentVisibility();

  const isNotExistNode = async ({catalog, schema, type, node}: IUrlStateHandler['urlState']) => {
    let res;
    if (node) {
      const fullName = `${catalog}.${schema}.${node}`;
      if (type === EnumNodeType.VOLUME) {
        res = await getVolumeDetail(workspaceId, fullName, true);
      } else if (type === EnumNodeType.TABLE) {
        res = await getTableDetail(workspaceId, fullName, true);
      } else if (type === EnumNodeType.OPERATOR) {
        res = await getOperatorDetail(workspaceId, fullName, true);
      } else if (type === EnumNodeType.DATASET) {
        res = await getDatasetDetail(workspaceId, fullName, true);
      } else if (type === EnumNodeType.MODEL) {
        res = await getModelDetail(workspaceId, fullName, true);
      }
    } else if (schema) {
      res = await getSchemaDetail(workspaceId, `${catalog}.${schema}`, true);
    } else if (catalog) {
      res = await getCatalogDetail(workspaceId, catalog, true);
    }
    if (!res?.success) {
      return true;
    }

    return false;
  };

  useEffect(() => {
    if (visibility === 'visible' && urlState.catalog && !urlState.isUnassigned) {
      isNotExistNode(urlState).then((isNotExist) => {
        if (isNotExist) {
          changeUrlFunReplace(
            (pre) => ({
              ...pre,
              catalog: CatalogType.SYSTEM,
              refresh: true,
              node: undefined,
              schema: undefined,
              type: undefined,
              tab: undefined,
              isDoris: undefined,
              isUnassigned: undefined
            }),
            true
          );
        }
      });
    }
  }, [visibility]);

  useEffect(() => {
    if (flags.DatabuilderPrivateSwitch) {
      // 私有化环境，workspaceId 参数会在此使用
      privateUserList(workspaceId).then((res) => {
        if (res.success) {
          setUserList(res.result.result);
        }
      });
    } else {
      // 公有云环境
      queryIamUserList().then((res) => {
        if (res.success) {
          setUserList(res.page.result);
        }
      });
    }
  }, [workspaceId]);

  useEffect(() => {
    setHasMetastore(authList[Privilege.CreateCatalog]);
  }, [authList]);

  return (
    <DividerLayout
      initWidth={300}
      minWidth={200}
      maxWidth={500}
      isSaveLocalStore={false}
      className={`${klass}`}
    >
      {/* 左侧树 */}
      <LeftTree
        ref={treeRef}
        urlState={urlState}
        changeUrlFun={changeUrlFun}
        changeUrlFunReplace={changeUrlFunReplace}
        hasMetastore={hasMetastore}
        canWrite={canWrite}
      />
      {/* 右侧内容 */}
      <RightContent
        urlState={urlState}
        changeUrlFun={changeUrlFun}
        changeUrlFunReplace={changeUrlFunReplace}
        handleTreeRefresh={handleTreeRefresh}
        userList={userList}
        hasMetastore={hasMetastore}
        canWrite={canWrite}
      />
    </DividerLayout>
  );
};

export default MetaData;
