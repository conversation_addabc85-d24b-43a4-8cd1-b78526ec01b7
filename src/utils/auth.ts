import {batchVerifyAuth, verifyAuth, verifySystemAdmin} from '@api/permission';
import {Privilege, PrivilegeType, ResourceType} from '@api/permission/type';
import {EAuth} from '@pages/index';
import {queryMetastore} from '@api/metastore';
import {isArray} from 'lodash';
import {queryWorkspaceDetail} from '@api/workspace';

interface IHasAccessProps {
  authKey: EAuth;
  globalMetastoreRead?: boolean;
}

/**
 * 判断是否拥有权限,若无authKey字段，则默认拥有权限
 *
 * @param authKey 权限key
 * @param globalMetastoreRead 是否拥有全局元数据读权限
 * @returns 是否拥有权限
 */
export function hasAccess({authKey, globalMetastoreRead}: IHasAccessProps): boolean {
  if (!authKey) {
    return true;
  }

  switch (authKey) {
    case EAuth.GlobalMetastoreRead:
      return !!globalMetastoreRead;
    default:
      return false;
  }
}

export const WorkspacePrivileges = [
  // 菜单权限
  Privilege.WorkspaceMenu,
  Privilege.CatalogMenu,
  Privilege.ComputeMenu,
  Privilege.IntegrationMenu,
  Privilege.WorkflowMenu,
  Privilege.WorkflowInstanceMenu,

  // 按钮权限
  // Privilege.DirCreate,
  // Privilege.FileImport,
  // Privilege.NotebookCreate,

  // Privilege.IntegrationComputeCreate,
  // Privilege.EtlComputeCreate,
  // Privilege.EtlJobTemplateCreate,
  // Privilege.AnalysisComputeCreate,
  // Privilege.ResourcePoolCreate,

  Privilege.UnstructuredIntegrationCreate,
  Privilege.UnstructuredIntegrationStop,
  Privilege.UnstructuredIntegrationDelete,
  Privilege.UnstructuredIntegrationExecute,

  Privilege.StructuredIntegrationCreate,
  Privilege.StructuredIntegrationExecute,
  Privilege.StructuredIntegrationPublish,
  Privilege.StructuredIntegrationDelete,
  Privilege.StructuredIntegrationModify,

  Privilege.WorkflowCreate,
  Privilege.WorkflowImport,

  Privilege.Modify
];

export const getGlobalPrivilege = async (): Promise<Partial<Record<Privilege, boolean>>> => {
  const [systemAdminRes, metastoreRes] = await Promise.all([verifySystemAdmin(), queryMetastore(true)]);

  const privileges = metastoreRes.result?.privileges;

  const isSystemAdmin = systemAdminRes.result;

  return {
    [Privilege.WorkspacesMenu]: true,
    [Privilege.MetastoreMenu]: isSystemAdmin || privileges?.includes(Privilege.View)
  };
};

// 获取空间内全部功能权限
export const getWorkspacePrivilege = async (workspaceId): Promise<Partial<Record<Privilege, boolean>>> => {
  if (!workspaceId) {
    return;
  }

  const [workspaceRes, metastoreRes] = await Promise.all([
    queryWorkspaceDetail({id: workspaceId}),
    queryMetastore(true, workspaceId)
  ]);

  const metaPrivileges = metastoreRes.result?.privileges;
  return {
    ...Object.fromEntries(
      WorkspacePrivileges.map((key: Privilege) => [key, workspaceRes.result?.privileges.includes(key)])
    ),
    [Privilege.CreateCatalog]: metaPrivileges?.includes(Privilege.CreateCatalog),
    [Privilege.CreateConnection]: metaPrivileges?.includes(Privilege.CreateConnection)
  };
};
