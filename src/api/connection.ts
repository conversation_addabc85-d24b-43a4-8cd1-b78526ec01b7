/**
 * 元数据 - 数据源管理
 */

import {request, BaseResponseType} from './apiFunction';
import {TConnectionType} from '@pages/MetaData/Connection/constants';
import {Privilege} from '@api/permission/type';

const connectionUrl = '/api/databuilder/v1/connection';

// 数据源 - 列表
export interface IConnection {
  id: string;
  /** 数据源名 */
  name: string;
  /** 数据源类型 */
  type: TConnectionType;
  /** 描述 */
  comment: string;
  /** 主机 */
  host: string;
  /** 端口 */
  port: string;
  /** 用户 */
  user: string;
  /** FTP/SFTP 密码 */
  passwd: string;
  /** 拥有者 */
  owner: string;
  /** 创建时间 */
  createdAt: string;
  /** 创建人 */
  createdBy: string;
  /** 修改时间 */
  updatedAt: string;
  /** 最近修改人 */
  updatedBy: string;
  /** 权限 */
  privileges?: Privilege[];
}

export interface IQueryConnectionListParams {
  pageNo?: number;
  pageSize?: number;
  orderBy?: string;
  asc?: boolean;
  filter?: string;
  type?: string;
}

/** 查询数据源列表 */
export function queryConnectionList(
  workspaceId: string,
  params: IQueryConnectionListParams,
  privileges?: Privilege
): BaseResponseType<{
  connections: Array<IConnection>;
  total: number;
}> {
  return request({
    url: connectionUrl,
    method: 'GET',
    params: {...params, workspaceId, privileges}
  });
}

export interface ICreateConnectionParams {
  id?: string;
  /** 数据源名 */
  name: string;
  /** 数据源类型 */
  type: TConnectionType;
  /** 描述 */
  comment: string;
  /** 主机 */
  host: string;
  /** 端口 */
  port: string;
  /** 用户 */
  user: string;
  /** FTP/SFTP 密码 */
  passwd: string;
}

/** 创建数据源 */
export function createConnection(
  workspaceId: string,
  params: ICreateConnectionParams
): BaseResponseType<IConnection> {
  return request({
    url: connectionUrl,
    method: 'POST',
    data: params,
    params: {workspaceId}
  });
}

/** 更新数据源 */
export function updateConnection(
  workspaceId: string,
  params: ICreateConnectionParams
): BaseResponseType<IConnection> {
  return request({
    url: `${connectionUrl}/${params.name}`,
    method: 'PUT',
    data: params,
    params: {workspaceId}
  });
}

/** 删除数据源 */
export function deleteConnection(workspaceId: string, names: string[]): BaseResponseType<IConnection> {
  console.log(names);
  return request({
    url: connectionUrl,
    method: 'DELETE',
    data: {
      connectionNames: names
    },
    params: {workspaceId}
  });
}

// 数据源 - 详情
export interface IQueryConnectionDetailParams {
  id: string;
}

/** 获取数据源详情 */
export function queryConnectionDetail(workspaceId: string, name: string): BaseResponseType<IConnection> {
  return request({
    url: `${connectionUrl}/${name}`,
    method: 'GET',
    params: {workspaceId}
  });
}

export interface ITestConnectionParams {
  environment: {
    computeId: string;
    workspaceId: string;
  };
  datasourceInfo: {
    type: TConnectionType;
    host: string;
    port: string;
    username: string;
    password: string;
    database?: string;
  };
}

export interface IConnectionTest {
  reachable: boolean;
}

/** 连通测试 */
export function testConnection(
  workspaceId: string,
  params: ITestConnectionParams
): BaseResponseType<IConnectionTest> {
  return request({
    url: '/api/databuilder/v1/connection/test',
    method: 'POST',
    data: params,
    params: {workspaceId}
  });
}
